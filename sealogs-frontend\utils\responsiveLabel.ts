import { useBreakpoints } from "@/components/hooks/useBreakpoints"

type BreakpointKey =
    | 'tiny'
    | 'small'
    | 'standard'
    | 'phablet'
    | 'tablet-sm'
    | 'tablet-md'
    | 'tablet-lg'
    | 'landscape'
    | 'laptop'
    | 'desktop'

// Hook version - use this at the component level
function useResponsiveLabel(breakpoint: BreakpointKey = 'phablet') {
    const bp = useBreakpoints()
    return (short: string, long: string) => bp[breakpoint] ? long : short
}

// Utility version - use this when you have breakpoint state available
function getResponsiveLabel(isAtBreakpoint: boolean, short: string, long: string) {
    return isAtBreakpoint ? long : short
}

// Legacy hook version for backward compatibility (but should be avoided in conditional rendering)
function responsiveLabel(short: string, long: string, breakpoint: BreakpointKey = 'phablet') {
    const bp = useBreakpoints()
    return bp[breakpoint] ? long : short
}

export default responsiveLabel
export { useResponsiveLabel, getResponsiveLabel, type BreakpointKey }
